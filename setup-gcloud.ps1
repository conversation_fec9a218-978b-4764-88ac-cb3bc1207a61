# Script PowerShell para configurar Google Cloud CLI e Cloud SQL Auth Proxy no Windows
# Requer PowerShell 5.1 ou superior

param(
    [switch]$SkipInstall,
    [string]$ProjectId
)

# Configurar política de execução temporariamente se necessário
$currentPolicy = Get-ExecutionPolicy
if ($currentPolicy -eq 'Restricted') {
    Write-Warning "Política de execução restrita detectada. Você pode precisar executar:"
    Write-Warning "Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser"
    exit 1
}

# Função para imprimir mensagens coloridas
function Write-ColorMessage {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Color
}

function Write-Info {
    param([string]$Message)
    Write-ColorMessage "[INFO] $Message" "Green"
}

function Write-Warning {
    param([string]$Message)
    Write-ColorMessage "[WARNING] $Message" "Yellow"
}

function Write-Error {
    param([string]$Message)
    Write-ColorMessage "[ERROR] $Message" "Red"
}

function Write-Step {
    param([string]$Message)
    Write-ColorMessage "[STEP] $Message" "Cyan"
}

# Verificar se está executando como administrador
function Test-Administrator {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

# Verificar se gcloud já está instalado
function Test-GcloudInstalled {
    try {
        $version = gcloud version 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Info "Google Cloud CLI já está instalado"
            gcloud version
            return $true
        }
    }
    catch {
        return $false
    }
    return $false
}

# Instalar Google Cloud CLI
function Install-GoogleCloudCLI {
    Write-Step "Instalando Google Cloud CLI..."
    
    # Verificar se Chocolatey está disponível
    if (Get-Command choco -ErrorAction SilentlyContinue) {
        Write-Info "Usando Chocolatey para instalar..."
        choco install gcloudsdk -y
        return
    }
    
    # Verificar se Scoop está disponível
    if (Get-Command scoop -ErrorAction SilentlyContinue) {
        Write-Info "Usando Scoop para instalar..."
        scoop bucket add extras
        scoop install gcloud
        return
    }
    
    # Instalação manual
    Write-Info "Fazendo download e instalação manual..."
    
    $downloadUrl = "https://dl.google.com/dl/cloudsdk/channels/rapid/GoogleCloudSDKInstaller.exe"
    $installerPath = "$env:TEMP\GoogleCloudSDKInstaller.exe"
    
    try {
        Write-Info "Baixando instalador..."
        Invoke-WebRequest -Uri $downloadUrl -OutFile $installerPath -UseBasicParsing
        
        Write-Info "Executando instalador..."
        Start-Process -FilePath $installerPath -Wait
        
        # Remover instalador temporário
        Remove-Item $installerPath -Force -ErrorAction SilentlyContinue
        
        Write-Info "Instalação concluída. Reinicie o PowerShell para usar o gcloud."
        Write-Warning "Pressione qualquer tecla para continuar após reiniciar o PowerShell..."
        $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
        
    }
    catch {
        Write-Error "Erro durante a instalação: $($_.Exception.Message)"
        Write-Info "Por favor, baixe e instale manualmente de: $downloadUrl"
        exit 1
    }
}

# Fazer login no gcloud
function Invoke-GcloudLogin {
    Write-Step "Fazendo login no Google Cloud..."
    
    try {
        # Login interativo
        Write-Info "Iniciando login interativo..."
        gcloud auth login
        
        if ($LASTEXITCODE -ne 0) {
            throw "Falha no login do gcloud"
        }
        
        # Configurar credenciais padrão da aplicação
        Write-Step "Configurando credenciais padrão da aplicação..."
        gcloud auth application-default login
        
        if ($LASTEXITCODE -ne 0) {
            throw "Falha na configuração das credenciais padrão"
        }
        
        Write-Info "Login realizado com sucesso!"
    }
    catch {
        Write-Error "Erro durante o login: $($_.Exception.Message)"
        exit 1
    }
}

# Configurar projeto
function Set-GcloudProject {
    param([string]$ProjectId)
    
    Write-Step "Configurando projeto do Google Cloud..."
    
    try {
        # Listar projetos disponíveis
        Write-Info "Projetos disponíveis:"
        gcloud projects list
        
        if (-not $ProjectId) {
            $ProjectId = Read-Host "Digite o ID do projeto que deseja usar (ou pressione Enter para pular)"
        }
        
        if ($ProjectId) {
            gcloud config set project $ProjectId
            if ($LASTEXITCODE -eq 0) {
                Write-Info "Projeto configurado: $ProjectId"
            } else {
                Write-Warning "Falha ao configurar o projeto: $ProjectId"
            }
        } else {
            Write-Warning "Projeto não configurado. Você pode configurar depois com: gcloud config set project PROJECT_ID"
        }
    }
    catch {
        Write-Error "Erro ao configurar projeto: $($_.Exception.Message)"
    }
}

# Instalar Cloud SQL Auth Proxy
function Install-CloudSQLProxy {
    Write-Step "Instalando Cloud SQL Auth Proxy..."
    
    try {
        # Criar diretório para binários locais
        $localBinPath = "$env:USERPROFILE\.local\bin"
        if (-not (Test-Path $localBinPath)) {
            New-Item -ItemType Directory -Path $localBinPath -Force | Out-Null
        }
        
        # Download do Cloud SQL Auth Proxy
        $proxyUrl = "https://dl.google.com/cloudsql/cloud_sql_proxy.windows.amd64.exe"
        $proxyPath = "$localBinPath\cloud_sql_proxy.exe"
        
        Write-Info "Baixando Cloud SQL Auth Proxy..."
        Invoke-WebRequest -Uri $proxyUrl -OutFile $proxyPath -UseBasicParsing
        
        # Adicionar ao PATH se necessário
        $currentPath = [Environment]::GetEnvironmentVariable("PATH", "User")
        if ($currentPath -notlike "*$localBinPath*") {
            Write-Info "Adicionando $localBinPath ao PATH do usuário..."
            [Environment]::SetEnvironmentVariable("PATH", "$currentPath;$localBinPath", "User")
            $env:PATH += ";$localBinPath"
        }
        
        Write-Info "Cloud SQL Auth Proxy instalado com sucesso!"
        Write-Info "Localização: $proxyPath"
        
        # Testar se o proxy funciona
        & $proxyPath --version
        if ($LASTEXITCODE -eq 0) {
            Write-Info "Cloud SQL Auth Proxy está funcionando corretamente!"
        }
    }
    catch {
        Write-Error "Erro ao instalar Cloud SQL Auth Proxy: $($_.Exception.Message)"
    }
}

# Função principal
function Main {
    Write-Info "=== Configuração do Google Cloud CLI e Cloud SQL Auth Proxy ==="
    Write-Info "Sistema: Windows PowerShell"
    
    # Verificar versão do PowerShell
    Write-Info "PowerShell versão: $($PSVersionTable.PSVersion)"
    
    # Verificar se gcloud já está instalado
    if (-not $SkipInstall -and -not (Test-GcloudInstalled)) {
        Install-GoogleCloudCLI
    }
    
    # Verificar novamente após instalação
    if (-not (Test-GcloudInstalled)) {
        Write-Error "Google Cloud CLI não foi encontrado. Verifique a instalação."
        exit 1
    }
    
    # Fazer login
    Invoke-GcloudLogin
    
    # Configurar projeto
    Set-GcloudProject -ProjectId $ProjectId
    
    # Instalar Cloud SQL Auth Proxy
    Install-CloudSQLProxy
    
    Write-Info "=== Configuração concluída com sucesso! ==="
    Write-Info ""
    Write-Info "Próximos passos:"
    Write-Info "1. Para usar o Cloud SQL Auth Proxy:"
    Write-Info "   cloud_sql_proxy.exe -instances=PROJECT_ID:REGION:INSTANCE_NAME=tcp:5432"
    Write-Info ""
    Write-Info "2. Para verificar a configuração:"
    Write-Info "   gcloud auth list"
    Write-Info "   gcloud config list"
    Write-Info ""
    Write-Info "3. Para mais informações:"
    Write-Info "   gcloud --help"
    Write-Info ""
    Write-Warning "Nota: Reinicie o PowerShell se os comandos não forem reconhecidos."
}

# Executar função principal
try {
    Main
}
catch {
    Write-Error "Erro durante a execução: $($_.Exception.Message)"
    exit 1
}
