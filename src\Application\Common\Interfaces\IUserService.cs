using HighCapital.AuthenticationService.Application.Common.Models;
using HighCapital.AuthenticationService.Domain.Entities;

namespace HighCapital.AuthenticationService.Application.Common.Interfaces;

public interface IUserService
{
    Task<Result<int>> RegisterUserAsync(string firstName, string lastName, string email, string password, CancellationToken cancellationToken = default);
    Task<Result<string>> LoginUserAsync(string email, string password, CancellationToken cancellationToken = default);
    Task<Result<User?>> GetUserByIdAsync(int id, CancellationToken cancellationToken = default);
    Task<Result<User?>> GetUserByEmailAsync(string email, CancellationToken cancellationToken = default);
    Task<Result<IEnumerable<User>>> GetAllUsersAsync(CancellationToken cancellationToken = default);
    Task<Result> UpdateUserAsync(int id, string firstName, string lastName, string email, CancellationToken cancellationToken = default);
    Task<Result> DeleteUserAsync(int id, CancellationToken cancellationToken = default);
}
