using HighCapital.AuthenticationService.Application.Common.Interfaces;
using HighCapital.AuthenticationService.Application.Common.Models;
using HighCapital.AuthenticationService.Domain.Entities;
using Microsoft.Extensions.Logging;

namespace HighCapital.AuthenticationService.Application.Services;

public class UserService : IUserService
{
    private readonly IApplicationDbContext _context;
    private readonly IIdentityService _identityService;
    private readonly IJwtTokenService _jwtTokenService;
    private readonly ILogger<UserService> _logger;

    public UserService(
        IApplicationDbContext context,
        IIdentityService identityService,
        IJwtTokenService jwtTokenService,
        ILogger<UserService> logger)
    {
        _context = context;
        _identityService = identityService;
        _jwtTokenService = jwtTokenService;
        _logger = logger;
    }

    public async Task<Result<int>> RegisterUserAsync(string firstName, string lastName, string email, string password, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Attempting to register user with email: {Email}", email);

            // Check if user already exists in domain
            var existingUser = await _context.Users
                .FirstOrDefaultAsync(u => u.Email == email, cancellationToken);

            if (existingUser != null)
            {
                return Result<int>.Failure(new[] { "User with this email already exists." });
            }

            // Create user in Identity system
            var (identityResult, userId) = await _identityService.CreateUserAsync(email, password, firstName, lastName);

            if (!identityResult.Succeeded)
            {
                return Result<int>.Failure(identityResult.Errors);
            }

            // Create domain user entity
            var domainUser = new User
            {
                FirstName = firstName,
                LastName = lastName,
                Email = email
            };

            _context.Users.Add(domainUser);
            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("User registered successfully with ID: {UserId}", domainUser.Id);

            return Result<int>.Success(domainUser.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while registering user with email: {Email}", email);
            return Result<int>.Failure(new[] { "An error occurred while registering the user." });
        }
    }

    public async Task<Result<string>> LoginUserAsync(string email, string password, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Attempting to login user with email: {Email}", email);

            var (identityResult, userId, userName, roles) = await _identityService.AuthenticateAsync(email, password);

            if (!identityResult.Succeeded)
            {
                return Result<string>.Failure(identityResult.Errors);
            }

            // Generate JWT token
            var token = await _jwtTokenService.GenerateTokenAsync(userId, userName ?? email, roles);

            _logger.LogInformation("User logged in successfully: {Email}", email);

            return Result<string>.Success(token);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while logging in user with email: {Email}", email);
            return Result<string>.Failure(new[] { "An error occurred during login." });
        }
    }

    public async Task<Result<User?>> GetUserByIdAsync(int id, CancellationToken cancellationToken = default)
    {
        try
        {
            var user = await _context.Users
                .FirstOrDefaultAsync(u => u.Id == id, cancellationToken);

            return Result<User?>.Success(user);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while getting user by ID: {UserId}", id);
            return Result<User?>.Failure(new[] { "An error occurred while retrieving the user." });
        }
    }

    public async Task<Result<User?>> GetUserByEmailAsync(string email, CancellationToken cancellationToken = default)
    {
        try
        {
            var user = await _context.Users
                .FirstOrDefaultAsync(u => u.Email == email, cancellationToken);

            return Result<User?>.Success(user);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while getting user by email: {Email}", email);
            return Result<User?>.Failure(new[] { "An error occurred while retrieving the user." });
        }
    }

    public async Task<Result<IEnumerable<User>>> GetAllUsersAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var users = await _context.Users.ToListAsync(cancellationToken);
            return Result<IEnumerable<User>>.Success(users);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while getting all users");
            return Result<IEnumerable<User>>.Failure(new[] { "An error occurred while retrieving users." });
        }
    }

    public async Task<Result> UpdateUserAsync(int id, string firstName, string lastName, string email, CancellationToken cancellationToken = default)
    {
        try
        {
            var user = await _context.Users
                .FirstOrDefaultAsync(u => u.Id == id, cancellationToken);

            if (user == null)
            {
                return Result.Failure(new[] { "User not found." });
            }

            // Check if email is being changed and if it's unique
            if (user.Email != email)
            {
                var existingUser = await _context.Users
                    .FirstOrDefaultAsync(u => u.Email == email && u.Id != id, cancellationToken);

                if (existingUser != null)
                {
                    return Result.Failure(new[] { "Email is already in use." });
                }
            }

            user.FirstName = firstName;
            user.LastName = lastName;
            user.Email = email;

            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("User updated successfully: {UserId}", id);

            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while updating user: {UserId}", id);
            return Result.Failure(new[] { "An error occurred while updating the user." });
        }
    }

    public async Task<Result> DeleteUserAsync(int id, CancellationToken cancellationToken = default)
    {
        try
        {
            var user = await _context.Users
                .FirstOrDefaultAsync(u => u.Id == id, cancellationToken);

            if (user == null)
            {
                return Result.Failure(new[] { "User not found." });
            }

            _context.Users.Remove(user);
            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("User deleted successfully: {UserId}", id);

            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while deleting user: {UserId}", id);
            return Result.Failure(new[] { "An error occurred while deleting the user." });
        }
    }
}
