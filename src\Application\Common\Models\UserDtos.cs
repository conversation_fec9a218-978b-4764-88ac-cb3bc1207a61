using System.ComponentModel.DataAnnotations;

namespace HighCapital.AuthenticationService.Application.Common.Models;

/// <summary>
/// Data Transfer Object for user registration requests.
/// Contains all necessary information to create a new user account.
/// </summary>
public class RegisterUserRequest
{
    /// <summary>
    /// User's first name. Must be between 1 and 50 characters.
    /// </summary>
    /// <example>John</example>
    [Required(ErrorMessage = "First name is required")]
    [StringLength(50, MinimumLength = 1, ErrorMessage = "First name must be between 1 and 50 characters")]
    public string FirstName { get; set; } = string.Empty;

    /// <summary>
    /// User's last name. Must be between 1 and 50 characters.
    /// </summary>
    /// <example>Doe</example>
    [Required(ErrorMessage = "Last name is required")]
    [StringLength(50, MinimumLength = 1, ErrorMessage = "Last name must be between 1 and 50 characters")]
    public string LastName { get; set; } = string.Empty;

    /// <summary>
    /// User's email address. Must be unique and in valid email format.
    /// </summary>
    /// <example><EMAIL></example>
    [Required(ErrorMessage = "Email is required")]
    [EmailAddress(ErrorMessage = "Please provide a valid email address")]
    [StringLength(256, ErrorMessage = "Email cannot exceed 256 characters")]
    public string Email { get; set; } = string.Empty;

    /// <summary>
    /// User's password. Must meet security requirements.
    /// </summary>
    /// <example>SecurePassword123!</example>
    [Required(ErrorMessage = "Password is required")]
    [StringLength(100, MinimumLength = 8, ErrorMessage = "Password must be between 8 and 100 characters")]
    public string Password { get; set; } = string.Empty;
}

/// <summary>
/// Data Transfer Object for user login requests.
/// Contains credentials needed for authentication.
/// </summary>
public class LoginUserRequest
{
    /// <summary>
    /// User's email address used for authentication.
    /// </summary>
    /// <example><EMAIL></example>
    [Required(ErrorMessage = "Email is required")]
    [EmailAddress(ErrorMessage = "Please provide a valid email address")]
    public string Email { get; set; } = string.Empty;

    /// <summary>
    /// User's password for authentication.
    /// </summary>
    /// <example>SecurePassword123!</example>
    [Required(ErrorMessage = "Password is required")]
    public string Password { get; set; } = string.Empty;
}

/// <summary>
/// Data Transfer Object for update user requests.
/// Contains user information that can be modified.
/// </summary>
public class UpdateUserRequest
{
    /// <summary>
    /// Updated first name. Must be between 1 and 50 characters.
    /// </summary>
    /// <example>John</example>
    [Required(ErrorMessage = "First name is required")]
    [StringLength(50, MinimumLength = 1, ErrorMessage = "First name must be between 1 and 50 characters")]
    public string FirstName { get; set; } = string.Empty;

    /// <summary>
    /// Updated last name. Must be between 1 and 50 characters.
    /// </summary>
    /// <example>Doe</example>
    [Required(ErrorMessage = "Last name is required")]
    [StringLength(50, MinimumLength = 1, ErrorMessage = "Last name must be between 1 and 50 characters")]
    public string LastName { get; set; } = string.Empty;

    /// <summary>
    /// Updated email address. Must be unique and in valid email format.
    /// </summary>
    /// <example><EMAIL></example>
    [Required(ErrorMessage = "Email is required")]
    [EmailAddress(ErrorMessage = "Please provide a valid email address")]
    [StringLength(256, ErrorMessage = "Email cannot exceed 256 characters")]
    public string Email { get; set; } = string.Empty;
}

/// <summary>
/// Data Transfer Object for user response data.
/// Contains public user information returned by the API.
/// </summary>
public class UserResponse
{
    /// <summary>
    /// User's unique identifier.
    /// </summary>
    /// <example>123</example>
    public int Id { get; set; }

    /// <summary>
    /// User's first name.
    /// </summary>
    /// <example>John</example>
    public string FirstName { get; set; } = string.Empty;

    /// <summary>
    /// User's last name.
    /// </summary>
    /// <example>Doe</example>
    public string LastName { get; set; } = string.Empty;

    /// <summary>
    /// User's email address.
    /// </summary>
    /// <example><EMAIL></example>
    public string Email { get; set; } = string.Empty;

    /// <summary>
    /// User's full name (concatenation of first and last name).
    /// </summary>
    /// <example>John Doe</example>
    public string FullName => $"{FirstName} {LastName}";

    /// <summary>
    /// Date and time when the user was created.
    /// </summary>
    /// <example>2024-01-15T10:30:00Z</example>
    public DateTime Created { get; set; }

    /// <summary>
    /// Date and time when the user was last modified.
    /// </summary>
    /// <example>2024-01-20T14:45:00Z</example>
    public DateTime? LastModified { get; set; }
}
