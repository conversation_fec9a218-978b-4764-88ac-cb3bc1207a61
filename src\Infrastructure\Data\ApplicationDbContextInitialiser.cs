using HighCapital.AuthenticationService.Domain.Constants;
using HighCapital.AuthenticationService.Infrastructure.Identity;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;

namespace HighCapital.AuthenticationService.Infrastructure.Data;

public static class InitialiserExtensions
{
    public static async Task InitialiseDatabaseAsync(this WebApplication app)
    {
        using var scope = app.Services.CreateScope();

        var initialiser = scope.ServiceProvider.GetRequiredService<ApplicationDbContextInitialiser>();

        await initialiser.InitialiseAsync();
        await initialiser.SeedAsync();
    }
}

public class ApplicationDbContextInitialiser
{
    private readonly ILogger<ApplicationDbContextInitialiser> _logger;
    private readonly ApplicationDbContext _context;
    private readonly UserManager<ApplicationUser> _userManager;
    private readonly RoleManager<IdentityRole> _roleManager;

    public ApplicationDbContextInitialiser(ILogger<ApplicationDbContextInitialiser> logger, ApplicationDbContext context, UserManager<ApplicationUser> userManager, RoleManager<IdentityRole> roleManager)
    {
        _logger = logger;
        _context = context;
        _userManager = userManager;
        _roleManager = roleManager;
    }

    public async Task InitialiseAsync()
    {
        try
        {
            // See https://jasontaylor.dev/ef-core-database-initialisation-strategies
            var skip = Environment.GetEnvironmentVariable("SKIP_DB_INIT");
            if (string.Equals(skip, "true", StringComparison.OrdinalIgnoreCase))
            {
                _logger.LogInformation("Skipping database initialization due to SKIP_DB_INIT=true");
                return;
            }
            if (!await _context.Database.CanConnectAsync())
            {
                _logger.LogWarning("Skipping database initialization: cannot connect to configured database.");
                return;
            }
            
            _logger.LogInformation("Running database migrations...");
            
            // For SQLite, ensure database is created
            if (_context.Database.ProviderName == "Microsoft.EntityFrameworkCore.Sqlite")
            {
                await _context.Database.EnsureCreatedAsync();
                _logger.LogInformation("SQLite database created or verified.");
            }
            else
            {
                await _context.Database.MigrateAsync();
            }
            
            _logger.LogInformation("Database initialization completed successfully.");
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Database initialisation skipped due to exception.");
            // Don't rethrow so that tools like NSwag (design-time) don't fail build when DB not available
        }
    }

    public async Task SeedAsync()
    {
        try
        {
            await TrySeedAsync();
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Database seeding skipped due to exception.");
            // Swallow in design-time scenarios
        }
    }

    public async Task TrySeedAsync()
    {
        if (!await _context.Database.CanConnectAsync())
        {
            _logger.LogWarning("Skipping seeding: cannot connect to database.");
            return;
        }
        // Default roles
        var administratorRole = new IdentityRole(Roles.Administrator);

        if (_roleManager.Roles.All(r => r.Name != administratorRole.Name))
        {
            await _roleManager.CreateAsync(administratorRole);
        }

        // Default users
        var administrator = new ApplicationUser 
        { 
            UserName = "administrator@localhost", 
            Email = "administrator@localhost",
            FirstName = "Administrator",
            LastName = "User"
        };

        if (_userManager.Users.All(u => u.UserName != administrator.UserName))
        {
            await _userManager.CreateAsync(administrator, "Administrator1!");
            if (!string.IsNullOrWhiteSpace(administratorRole.Name))
            {
                await _userManager.AddToRolesAsync(administrator, new [] { administratorRole.Name });
            }
        }

        // Default data
        // Adicione seus dados iniciais aqui
        // if (!_context.Products.Any())
        // {
        //     _context.Products.Add(new Product { Name = "Sample Product", Price = 10.00m });
        //     await _context.SaveChangesAsync();
        // }
    }
}
