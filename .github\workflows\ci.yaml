# Nome do workflow
name: ci-dotnet-workflow

on: 
  pull_request:
    branches:
      - develop
jobs:
  build-and-test:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup .NET 9
        uses: actions/setup-dotnet@v4
        with:
          dotnet-version: '9.0.x' 

      - name: Restore dependencies
        run: dotnet restore

      - name: Build project
        run: dotnet build --configuration Release --no-restore

      - name: Build and Test
        run: dotnet test HighCapital.AuthenticationService.sln --configuration Debug --verbosity normal