﻿using FluentAssertions;
using HighCapital.AuthenticationService.Application.Common.Interfaces;
using HighCapital.AuthenticationService.Application.Common.Models;
using HighCapital.AuthenticationService.Domain.Entities;
using HighCapital.AuthenticationService.Service.Controllers;
using Microsoft.AspNetCore.Mvc;
using Moq;
using NUnit.Framework;

namespace HighCapital.AuthenticationService.Application.UnitTests.Controllers;

[TestFixture]
public class UsersControllerTests
{
    private Mock<IUserService> _mockUserService;
    private UsersController _controller;

    [SetUp]
    public void SetUp()
    {
        _mockUserService = new Mock<IUserService>();
        _controller = new UsersController(_mockUserService.Object);
    }

    [Test]
    public async Task GetAllUsers_WhenSuccess_ShouldReturnOkWithUsers()
    {
        // Arrange
        var users = new List<User> 
        { 
            new User { Id = 1, FirstName = "John", LastName = "Doe", Email = "<EMAIL>" },
            new User { Id = 2, FirstName = "Jane", LastName = "Smith", Email = "<EMAIL>" }
        };
        _mockUserService
            .Setup(s => s.GetAllUsersAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result<IEnumerable<User>>.Success(users));

        // Act
        var result = await _controller.GetAllUsers();

        // Assert
        result.Should().BeOfType<OkObjectResult>();
        var okResult = (OkObjectResult)result;
        okResult.Value.Should().BeEquivalentTo(users);
    }

    [Test]
    public async Task GetAllUsers_WhenServiceFails_ShouldReturnBadRequest()
    {
        // Arrange
        var errors = new[] { "Database connection failed" };
        _mockUserService
            .Setup(s => s.GetAllUsersAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result<IEnumerable<User>>.Failure(errors));

        // Act
        var result = await _controller.GetAllUsers();

        // Assert
        result.Should().BeOfType<BadRequestObjectResult>();
        var badRequestResult = (BadRequestObjectResult)result;
        badRequestResult.Value.Should().BeEquivalentTo(new { errors });
    }

    [Test]
    public async Task GetUser_WhenUserExists_ShouldReturnOkWithUser()
    {
        // Arrange
        const int userId = 1;
        var user = new User { Id = userId, FirstName = "John", LastName = "Doe", Email = "<EMAIL>" };
        _mockUserService
            .Setup(s => s.GetUserByIdAsync(userId, It.IsAny<CancellationToken>()))
            .Returns(Task.FromResult(Result<User?>.Success(user)));

        // Act
        var result = await _controller.GetUser(userId);

        // Assert
        result.Should().BeOfType<OkObjectResult>();
        var okResult = (OkObjectResult)result;
        okResult.Value.Should().BeEquivalentTo(user);
    }

    [Test]
    public async Task GetUser_WhenUserNotFound_ShouldReturnNotFound()
    {
        // Arrange
        const int userId = 999;
        _mockUserService
            .Setup(s => s.GetUserByIdAsync(userId, It.IsAny<CancellationToken>()))
            .Returns(Task.FromResult(Result<User?>.Success(null)));

        // Act
        var result = await _controller.GetUser(userId);

        // Assert
        result.Should().BeOfType<NotFoundObjectResult>();
        var notFoundResult = (NotFoundObjectResult)result;
        notFoundResult.Value.Should().BeEquivalentTo(new { message = "User not found" });
    }

    [Test]
    public async Task GetUser_WhenServiceFails_ShouldReturnBadRequest()
    {
        // Arrange
        const int userId = 1;
        var errors = new[] { "User service error" };
        _mockUserService
            .Setup(s => s.GetUserByIdAsync(userId, It.IsAny<CancellationToken>()))
            .Returns(Task.FromResult(Result<User?>.Failure(errors)));

        // Act
        var result = await _controller.GetUser(userId);

        // Assert
        result.Should().BeOfType<BadRequestObjectResult>();
        var badRequestResult = (BadRequestObjectResult)result;
        badRequestResult.Value.Should().BeEquivalentTo(new { errors });
    }

    [Test]
    public async Task UpdateUser_WhenSuccess_ShouldReturnOkWithMessage()
    {
        // Arrange
        const int userId = 1;
        var request = new HighCapital.AuthenticationService.Service.Controllers.UpdateUserRequest
        {
            FirstName = "John",
            LastName = "Doe",
            Email = "<EMAIL>"
        };
        _mockUserService
            .Setup(s => s.UpdateUserAsync(userId, request.FirstName, request.LastName, request.Email, It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success());

        // Act
        var result = await _controller.UpdateUser(userId, request);

        // Assert
        result.Should().BeOfType<OkObjectResult>();
        var okResult = (OkObjectResult)result;
        okResult.Value.Should().BeEquivalentTo(new { message = "User updated successfully" });
    }

    [Test]
    public async Task UpdateUser_WhenServiceFails_ShouldReturnBadRequest()
    {
        // Arrange
        const int userId = 1;
        var request = new HighCapital.AuthenticationService.Service.Controllers.UpdateUserRequest
        {
            FirstName = "John",
            LastName = "Doe",
            Email = "invalid-email"
        };
        var errors = new[] { "Invalid email format" };
        _mockUserService
            .Setup(s => s.UpdateUserAsync(userId, request.FirstName, request.LastName, request.Email, It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Failure(errors));

        // Act
        var result = await _controller.UpdateUser(userId, request);

        // Assert
        result.Should().BeOfType<BadRequestObjectResult>();
        var badRequestResult = (BadRequestObjectResult)result;
        badRequestResult.Value.Should().BeEquivalentTo(new { errors });
    }

    [Test]
    public async Task DeleteUser_WhenSuccess_ShouldReturnOkWithMessage()
    {
        // Arrange
        const int userId = 1;
        _mockUserService
            .Setup(s => s.DeleteUserAsync(userId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success());

        // Act
        var result = await _controller.DeleteUser(userId);

        // Assert
        result.Should().BeOfType<OkObjectResult>();
        var okResult = (OkObjectResult)result;
        okResult.Value.Should().BeEquivalentTo(new { message = "User deleted successfully" });
    }

    [Test]
    public async Task DeleteUser_WhenServiceFails_ShouldReturnBadRequest()
    {
        // Arrange
        const int userId = 999;
        var errors = new[] { "User not found" };
        _mockUserService
            .Setup(s => s.DeleteUserAsync(userId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Failure(errors));

        // Act
        var result = await _controller.DeleteUser(userId);

        // Assert
        result.Should().BeOfType<BadRequestObjectResult>();
        var badRequestResult = (BadRequestObjectResult)result;
        badRequestResult.Value.Should().BeEquivalentTo(new { errors });
    }

    [Test]
    public async Task DeleteUser_WhenUserHasActiveSessions_ShouldReturnBadRequest()
    {
        // Arrange
        const int userId = 1;
        var errors = new[] { "Cannot delete user: user has active sessions" };
        _mockUserService
            .Setup(s => s.DeleteUserAsync(userId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Failure(errors));

        // Act
        var result = await _controller.DeleteUser(userId);

        // Assert
        result.Should().BeOfType<BadRequestObjectResult>();
        _mockUserService.Verify(s => s.DeleteUserAsync(userId, It.IsAny<CancellationToken>()), Times.Once);
    }
}
