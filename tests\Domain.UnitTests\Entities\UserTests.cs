using FluentAssertions;
using HighCapital.AuthenticationService.Domain.Common;
using HighCapital.AuthenticationService.Domain.Entities;
using NUnit.Framework;

namespace HighCapital.AuthenticationService.Domain.UnitTests.Entities;

[TestFixture]
public class UserTests
{
    [Test]
    public void User_WhenCreated_ShouldHaveDefaultValues()
    {
        // Act
        var user = new User();

        // Assert
        user.Id.Should().Be(0);
        user.FirstName.Should().BeEmpty();
        user.LastName.Should().BeEmpty();
        user.Email.Should().BeEmpty();
    }

    [Test]
    public void User_WhenPropertiesSet_ShouldRetainValues()
    {
        // Arrange
        const int expectedId = 123;
        const string expectedFirstName = "John";
        const string expectedLastName = "Doe";
        const string expectedEmail = "<EMAIL>";

        // Act
        var user = new User
        {
            Id = expectedId,
            FirstName = expectedFirstName,
            LastName = expectedLastName,
            Email = expectedEmail
        };

        // Assert
        user.Id.Should().Be(expectedId);
        user.FirstName.Should().Be(expectedFirstName);
        user.LastName.Should().Be(expectedLastName);
        user.Email.Should().Be(expectedEmail);
    }

    [Test]
    public void User_ShouldInheritFromBaseAuditableEntity()
    {
        // Arrange & Act
        var user = new User();

        // Assert
        user.Should().BeAssignableTo<BaseAuditableEntity>();
        user.Created.Should().Be(default(DateTimeOffset));
        user.CreatedBy.Should().BeNull();
        user.LastModified.Should().Be(default(DateTimeOffset));
        user.LastModifiedBy.Should().BeNull();
    }

    [Test]
    public void User_WhenCompared_ShouldUseIdForEquality()
    {
        // Arrange
        var user1 = new User { Id = 1, FirstName = "John", LastName = "Doe" };
        var user2 = new User { Id = 1, FirstName = "Jane", LastName = "Smith" };
        var user3 = new User { Id = 2, FirstName = "John", LastName = "Doe" };

        // Act & Assert
        user1.Should().BeEquivalentTo(user2, options => options.Including(u => u.Id));
        user1.Should().NotBeEquivalentTo(user3, options => options.Including(u => u.Id));
    }
}
