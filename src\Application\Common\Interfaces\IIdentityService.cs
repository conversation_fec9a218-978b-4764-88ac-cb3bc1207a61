using HighCapital.AuthenticationService.Application.Common.Models;

namespace HighCapital.AuthenticationService.Application.Common.Interfaces;

public interface IIdentityService
{
    Task<string?> GetUserNameAsync(string userId);

    Task<bool> IsInRoleAsync(string userId, string role);

    Task<bool> AuthorizeAsync(string userId, string policyName);

    Task<(Result Result, string UserId)> CreateUserAsync(string userName, string password, string firstName, string lastName);

    Task<Result> DeleteUserAsync(string userId);

    Task<(Result Result, string UserId, string? UserName, IEnumerable<string> Roles)> AuthenticateAsync(string userName, string password);
}
