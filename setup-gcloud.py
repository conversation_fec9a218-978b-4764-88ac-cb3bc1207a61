#!/usr/bin/env python3
"""
Script Python multiplataforma para configurar Google Cloud CLI e Cloud SQL Auth Proxy
Funciona em Windows, macOS e Linux
"""

import os
import sys
import platform
import subprocess
import urllib.request
import shutil
from pathlib import Path
import argparse

class Colors:
    """Cores para output no terminal"""
    RED = '\033[0;31m'
    GREEN = '\033[0;32m'
    YELLOW = '\033[1;33m'
    BLUE = '\033[0;34m'
    NC = '\033[0m'  # No Color

def print_message(message, color=Colors.GREEN):
    """Imprimir mensagem colorida"""
    print(f"{color}[INFO]{Colors.NC} {message}")

def print_warning(message):
    """Imprimir aviso"""
    print(f"{Colors.YELLOW}[WARNING]{Colors.NC} {message}")

def print_error(message):
    """Imprimir erro"""
    print(f"{Colors.RED}[ERROR]{Colors.NC} {message}")

def print_step(message):
    """Imprimir passo"""
    print(f"{Colors.BLUE}[STEP]{Colors.NC} {message}")

def detect_os():
    """Detectar sistema operacional"""
    system = platform.system().lower()
    if system == "linux":
        return "linux"
    elif system == "darwin":
        return "macos"
    elif system == "windows":
        return "windows"
    else:
        print_error(f"Sistema operacional não suportado: {system}")
        sys.exit(1)

def run_command(command, shell=True, check=True):
    """Executar comando do sistema"""
    try:
        result = subprocess.run(command, shell=shell, check=check, 
                              capture_output=True, text=True)
        return result
    except subprocess.CalledProcessError as e:
        print_error(f"Erro ao executar comando: {command}")
        print_error(f"Saída: {e.stdout}")
        print_error(f"Erro: {e.stderr}")
        if check:
            sys.exit(1)
        return e

def check_gcloud_installed():
    """Verificar se gcloud já está instalado"""
    try:
        result = run_command("gcloud version", check=False)
        if result.returncode == 0:
            print_message("Google Cloud CLI já está instalado")
            print(result.stdout)
            return True
    except FileNotFoundError:
        pass
    return False

def install_gcloud_linux():
    """Instalar gcloud no Linux"""
    print_step("Instalando Google Cloud CLI no Linux...")
    
    commands = [
        "sudo apt-get update",
        "sudo apt-get install -y apt-transport-https ca-certificates gnupg",
        "echo 'deb [signed-by=/usr/share/keyrings/cloud.google.gpg] https://packages.cloud.google.com/apt cloud-sdk main' | sudo tee -a /etc/apt/sources.list.d/google-cloud-sdk.list",
        "curl https://packages.cloud.google.com/apt/doc/apt-key.gpg | sudo apt-key --keyring /usr/share/keyrings/cloud.google.gpg add -",
        "sudo apt-get update",
        "sudo apt-get install -y google-cloud-cli"
    ]
    
    for cmd in commands:
        print_message(f"Executando: {cmd}")
        run_command(cmd)
    
    print_message("Google Cloud CLI instalado com sucesso no Linux")

def install_gcloud_macos():
    """Instalar gcloud no macOS"""
    print_step("Instalando Google Cloud CLI no macOS...")
    
    # Verificar se Homebrew está disponível
    try:
        run_command("brew --version", check=False)
        print_message("Usando Homebrew para instalar...")
        run_command("brew install --cask google-cloud-sdk")
    except:
        print_message("Homebrew não encontrado. Instalando manualmente...")
        
        # Download e instalação manual
        download_url = "https://dl.google.com/dl/cloudsdk/channels/rapid/downloads/google-cloud-cli-456.0.0-darwin-x86_64.tar.gz"
        temp_file = "/tmp/google-cloud-cli.tar.gz"
        
        print_message("Baixando Google Cloud CLI...")
        urllib.request.urlretrieve(download_url, temp_file)
        
        # Extrair
        run_command(f"cd /tmp && tar -xzf google-cloud-cli.tar.gz")
        run_command("cd /tmp/google-cloud-sdk && ./install.sh --quiet")
        
        # Adicionar ao PATH
        home = Path.home()
        bashrc = home / ".bashrc"
        zshrc = home / ".zshrc"
        
        path_export = 'export PATH="$HOME/google-cloud-sdk/bin:$PATH"'
        
        for rc_file in [bashrc, zshrc]:
            if rc_file.exists():
                with open(rc_file, "a") as f:
                    f.write(f"\n{path_export}\n")
    
    print_message("Google Cloud CLI instalado com sucesso no macOS")

def install_gcloud_windows():
    """Instalar gcloud no Windows"""
    print_step("Instalando Google Cloud CLI no Windows...")
    
    # Verificar se está no WSL
    try:
        with open("/proc/version", "r") as f:
            if "Microsoft" in f.read():
                print_message("WSL detectado. Usando instalação Linux...")
                install_gcloud_linux()
                return
    except FileNotFoundError:
        pass
    
    # Instalação nativa do Windows
    download_url = "https://dl.google.com/dl/cloudsdk/channels/rapid/GoogleCloudSDKInstaller.exe"
    temp_file = os.path.join(os.environ["TEMP"], "GoogleCloudSDKInstaller.exe")
    
    print_message("Baixando instalador do Google Cloud CLI...")
    urllib.request.urlretrieve(download_url, temp_file)
    
    print_message("Executando instalador...")
    run_command(f'"{temp_file}"')
    
    # Limpar arquivo temporário
    try:
        os.remove(temp_file)
    except:
        pass
    
    print_message("Google Cloud CLI instalado com sucesso no Windows")

def install_gcloud(os_type):
    """Instalar gcloud baseado no OS"""
    if os_type == "linux":
        install_gcloud_linux()
    elif os_type == "macos":
        install_gcloud_macos()
    elif os_type == "windows":
        install_gcloud_windows()
    else:
        print_error(f"Sistema operacional não suportado: {os_type}")
        sys.exit(1)

def gcloud_login():
    """Fazer login no gcloud"""
    print_step("Fazendo login no Google Cloud...")
    
    # Login interativo
    run_command("gcloud auth login")
    
    # Configurar credenciais padrão da aplicação
    print_step("Configurando credenciais padrão da aplicação...")
    run_command("gcloud auth application-default login")
    
    print_message("Login realizado com sucesso!")

def configure_project():
    """Configurar projeto (opcional)"""
    print_step("Configurando projeto do Google Cloud...")
    
    # Listar projetos disponíveis
    print_message("Projetos disponíveis:")
    result = run_command("gcloud projects list")
    print(result.stdout)
    
    project_id = input("Digite o ID do projeto que deseja usar (ou pressione Enter para pular): ").strip()
    
    if project_id:
        run_command(f"gcloud config set project {project_id}")
        print_message(f"Projeto configurado: {project_id}")
    else:
        print_warning("Projeto não configurado. Você pode configurar depois com: gcloud config set project PROJECT_ID")

def install_cloud_sql_proxy(os_type):
    """Instalar Cloud SQL Auth Proxy"""
    print_step("Instalando Cloud SQL Auth Proxy...")
    
    # Criar diretório para binários locais
    local_bin = Path.home() / ".local" / "bin"
    local_bin.mkdir(parents=True, exist_ok=True)
    
    # URLs de download baseado no OS
    urls = {
        "linux": "https://dl.google.com/cloudsql/cloud_sql_proxy.linux.amd64",
        "macos": "https://dl.google.com/cloudsql/cloud_sql_proxy.darwin.amd64",
        "windows": "https://dl.google.com/cloudsql/cloud_sql_proxy.windows.amd64.exe"
    }
    
    # Nome do arquivo
    filename = "cloud_sql_proxy.exe" if os_type == "windows" else "cloud_sql_proxy"
    proxy_path = local_bin / filename
    
    # Download
    print_message(f"Baixando Cloud SQL Auth Proxy para {os_type}...")
    urllib.request.urlretrieve(urls[os_type], str(proxy_path))
    
    # Tornar executável (Unix-like systems)
    if os_type != "windows":
        proxy_path.chmod(0o755)
    
    print_message("Cloud SQL Auth Proxy instalado com sucesso!")
    print_message(f"Localização: {proxy_path}")
    
    # Adicionar ao PATH se necessário
    path_env = os.environ.get("PATH", "")
    if str(local_bin) not in path_env:
        print_warning(f"Adicione {local_bin} ao seu PATH para usar o cloud_sql_proxy globalmente")

def main():
    """Função principal"""
    parser = argparse.ArgumentParser(description="Configurar Google Cloud CLI e Cloud SQL Auth Proxy")
    parser.add_argument("--skip-install", action="store_true", help="Pular instalação do gcloud")
    parser.add_argument("--project-id", help="ID do projeto para configurar")
    args = parser.parse_args()
    
    print_message("=== Configuração do Google Cloud CLI e Cloud SQL Auth Proxy ===")
    
    # Detectar OS
    os_type = detect_os()
    print_message(f"Sistema operacional detectado: {os_type}")
    
    # Verificar se gcloud já está instalado
    if not args.skip_install and not check_gcloud_installed():
        install_gcloud(os_type)
    
    # Verificar novamente após instalação
    if not check_gcloud_installed():
        print_error("Google Cloud CLI não foi encontrado após instalação")
        sys.exit(1)
    
    # Fazer login
    gcloud_login()
    
    # Configurar projeto
    if args.project_id:
        run_command(f"gcloud config set project {args.project_id}")
        print_message(f"Projeto configurado: {args.project_id}")
    else:
        configure_project()
    
    # Instalar Cloud SQL Auth Proxy
    install_cloud_sql_proxy(os_type)
    
    print_message("=== Configuração concluída com sucesso! ===")
    print_message("")
    print_message("Próximos passos:")
    print_message("1. Para usar o Cloud SQL Auth Proxy:")
    print_message("   cloud_sql_proxy -instances=PROJECT_ID:REGION:INSTANCE_NAME=tcp:5432")
    print_message("")
    print_message("2. Para verificar a configuração:")
    print_message("   gcloud auth list")
    print_message("   gcloud config list")
    print_message("")
    print_message("3. Para mais informações:")
    print_message("   gcloud --help")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print_error("\nOperação cancelada pelo usuário")
        sys.exit(1)
    except Exception as e:
        print_error(f"Erro inesperado: {e}")
        sys.exit(1)
